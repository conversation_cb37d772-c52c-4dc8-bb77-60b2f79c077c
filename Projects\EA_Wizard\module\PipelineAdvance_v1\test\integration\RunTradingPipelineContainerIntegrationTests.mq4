//+------------------------------------------------------------------+
//|                   RunTradingPipelineContainerIntegrationTests.mq4 |
//|                                            PipelineAdvance_v1   |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "TestTradingPipelineContainerIntegration.mqh"

//+------------------------------------------------------------------+
//| 腳本程序開始函數                                                 |
//+------------------------------------------------------------------+
void <PERSON>tar<PERSON>()
{
    Print("\n" + StringRepeat("=", 90));
    Print("  TradingPipelineContainer & TradingPipelineContainerManager 整合測試套件");
    Print(StringRepeat("=", 90));
    Print("開始時間: " + TimeToString(TimeCurrent()));
    Print(StringRepeat("=", 90));

    // 創建整合測試運行器
    TradingPipelineContainerIntegrationTest* integrationTest = new TradingPipelineContainerIntegrationTest();

    // 運行所有整合測試
    integrationTest.RunAllIntegrationTests();

    // 清理
    delete integrationTest;

    Print("\n" + StringRepeat("=", 90));
    Print("  TradingPipelineContainer 整合測試套件完成");
    Print("結束時間: " + TimeToString(TimeCurrent()));
    Print(StringRepeat("=", 90));
}

//+------------------------------------------------------------------+
//| 字符串重複函數                                                     |
//+------------------------------------------------------------------+
string StringRepeat(string str, int count)
{
    string result = "";
    for(int i = 0; i < count; i++)
    {
        result = result + str;
    }
    return result;
}
