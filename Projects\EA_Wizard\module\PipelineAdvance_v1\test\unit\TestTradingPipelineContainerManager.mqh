//+------------------------------------------------------------------+
//|                           TestTradingPipelineContainerManager.mqh |
//|                                            PipelineAdvance_v1   |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "../../TradingPipelineContainerManager.mqh"
#include "../../TradingPipelineContainer.mqh"
#include "../../TradingPipeline.mqh"

//+------------------------------------------------------------------+
//| 測試用的簡單交易流水線                                           |
//+------------------------------------------------------------------+
class TestManagerTradingPipeline : public TradingPipeline
{
private:
    string m_testMessage;
    bool m_shouldFail;

public:
    TestManagerTradingPipeline(string name, string testMessage = "測試執行", bool shouldFail = false)
        : TradingPipeline(name, "TestPipeline"),
          m_testMessage(testMessage),
          m_shouldFail(shouldFail)
    {
    }

protected:
    virtual void Main() override
    {
        if(m_shouldFail)
        {
            Print(StringFormat("[%s] 模擬執行失敗: %s", GetName(), m_testMessage));
        }
        else
        {
            Print(StringFormat("[%s] 執行成功: %s", GetName(), m_testMessage));
        }
    }
};

//+------------------------------------------------------------------+
//| TradingPipelineContainerManager 單元測試類                       |
//+------------------------------------------------------------------+
class TestTradingPipelineContainerManagerCase : public TestCase
{
private:
    TestRunner* m_runner;

public:
    TestTradingPipelineContainerManagerCase() : TestCase("TestTradingPipelineContainerManager")
    {
        m_runner = new TestRunner();
    }

    ~TestTradingPipelineContainerManagerCase()
    {
        if(m_runner != NULL)
        {
            delete m_runner;
            m_runner = NULL;
        }
    }

    virtual void RunTests() override
    {
        TestConstructor();
        TestBasicProperties();
        TestAddContainer();
        TestRemoveContainer();
        TestFindContainer();
        TestExecuteByEventType();
        TestExecuteAll();
        TestMaxContainersLimit();
        TestClear();
        TestEdgeCases();
        TestComplexScenario();

        // 顯示測試摘要
        m_runner.ShowSummary();
    }

protected:
    // 記錄測試結果的輔助方法
    void RecordResult(TestResult* result)
    {
        m_runner.RecordResult(result);
    }

private:
    // 測試構造函數
    void TestConstructor()
    {
        Print("--- 測試構造函數 ---");

        // 測試默認構造函數
        TradingPipelineContainerManager* manager1 = new TradingPipelineContainerManager();
        RecordResult(Assert::AssertNotNull("構造函數_默認參數", manager1));
        RecordResult(Assert::AssertEquals("構造函數_默認名稱", "TradingPipelineContainerManager", manager1.GetName()));
        RecordResult(Assert::AssertEquals("構造函數_默認類型", "ContainerManager", manager1.GetType()));
        RecordResult(Assert::AssertTrue("構造函數_初始啟用", manager1.IsEnabled()));
        RecordResult(Assert::AssertTrue("構造函數_初始為空", manager1.IsEmpty()));
        delete manager1;

        // 測試自定義參數構造函數
        TradingPipelineContainerManager* manager2 = new TradingPipelineContainerManager(
            "自定義管理器", "CustomManager", true, 5);
        RecordResult(Assert::AssertNotNull("構造函數_自定義參數", manager2));
        RecordResult(Assert::AssertEquals("構造函數_自定義名稱", "自定義管理器", manager2.GetName()));
        RecordResult(Assert::AssertEquals("構造函數_自定義類型", "CustomManager", manager2.GetType()));
        RecordResult(Assert::AssertEquals("構造函數_最大容量", 5, manager2.GetMaxContainers()));
        delete manager2;
    }

    // 測試基本屬性
    void TestBasicProperties()
    {
        Print("--- 測試基本屬性 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager(
            "屬性測試管理器", "PropertyTest", false, 8);

        RecordResult(Assert::AssertEquals("屬性_名稱", "屬性測試管理器", manager.GetName()));
        RecordResult(Assert::AssertEquals("屬性_類型", "PropertyTest", manager.GetType()));
        RecordResult(Assert::AssertEquals("屬性_最大容量", 8, manager.GetMaxContainers()));
        RecordResult(Assert::AssertEquals("屬性_初始數量", 0, manager.GetContainerCount()));
        RecordResult(Assert::AssertTrue("屬性_初始為空", manager.IsEmpty()));
        RecordResult(Assert::AssertFalse("屬性_初始未滿", manager.IsFull()));
        RecordResult(Assert::AssertTrue("屬性_有空位", manager.HasEmptySlot()));
        RecordResult(Assert::AssertFalse("屬性_初始未執行", manager.IsExecuted()));

        delete manager;
    }

    // 測試添加容器
    void TestAddContainer()
    {
        Print("--- 測試添加容器 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("添加測試管理器", "AddTest", false, 3);

        // 創建測試容器
        TradingPipelineContainer* container1 = new TradingPipelineContainer("容器1", "描述1", "Type1", TRADING_INIT);
        TradingPipelineContainer* container2 = new TradingPipelineContainer("容器2", "描述2", "Type2", TRADING_TICK);
        TradingPipelineContainer* container3 = new TradingPipelineContainer("容器3", "描述3", "Type3", TRADING_DEINIT);

        // 測試添加有效容器
        RecordResult(Assert::AssertTrue("添加_第一個容器", manager.AddContainer(container1)));
        RecordResult(Assert::AssertEquals("添加_數量檢查1", 1, manager.GetContainerCount()));
        RecordResult(Assert::AssertFalse("添加_不再為空", manager.IsEmpty()));

        RecordResult(Assert::AssertTrue("添加_第二個容器", manager.AddContainer(container2)));
        RecordResult(Assert::AssertEquals("添加_數量檢查2", 2, manager.GetContainerCount()));

        RecordResult(Assert::AssertTrue("添加_第三個容器", manager.AddContainer(container3)));
        RecordResult(Assert::AssertEquals("添加_數量檢查3", 3, manager.GetContainerCount()));
        RecordResult(Assert::AssertTrue("添加_已滿", manager.IsFull()));
        RecordResult(Assert::AssertFalse("添加_無空位", manager.HasEmptySlot()));

        // 測試添加超過容量
        TradingPipelineContainer* container4 = new TradingPipelineContainer("容器4", "描述4", "Type4", TRADING_TICK);
        RecordResult(Assert::AssertFalse("添加_超過容量", manager.AddContainer(container4)));
        RecordResult(Assert::AssertEquals("添加_數量不變", 3, manager.GetContainerCount()));
        delete container4;

        // 測試添加 NULL
        RecordResult(Assert::AssertFalse("添加_NULL容器", manager.AddContainer(NULL)));

        // 測試添加重複名稱容器
        TradingPipelineContainer* duplicateContainer = new TradingPipelineContainer("容器1", "重複名稱", "Duplicate", TRADING_TICK);
        RecordResult(Assert::AssertFalse("添加_重複名稱", manager.AddContainer(duplicateContainer)));
        delete duplicateContainer;

        delete manager;
    }

    // 測試移除容器
    void TestRemoveContainer()
    {
        Print("--- 測試移除容器 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("移除測試管理器", "RemoveTest", false, 5);

        // 添加測試容器
        TradingPipelineContainer* container1 = new TradingPipelineContainer("移除測試1", "描述1", "Type1", TRADING_INIT);
        TradingPipelineContainer* container2 = new TradingPipelineContainer("移除測試2", "描述2", "Type2", TRADING_TICK);
        manager.AddContainer(container1);
        manager.AddContainer(container2);

        RecordResult(Assert::AssertEquals("移除_初始數量", 2, manager.GetContainerCount()));

        // 測試按名稱移除
        RecordResult(Assert::AssertTrue("移除_按名稱成功", manager.RemoveContainerByName("移除測試1")));
        RecordResult(Assert::AssertEquals("移除_數量減少", 1, manager.GetContainerCount()));

        // 測試移除不存在的容器
        RecordResult(Assert::AssertFalse("移除_不存在", manager.RemoveContainerByName("不存在的容器")));
        RecordResult(Assert::AssertEquals("移除_數量不變", 1, manager.GetContainerCount()));

        // 測試移除剩餘容器
        RecordResult(Assert::AssertTrue("移除_最後一個", manager.RemoveContainerByName("移除測試2")));
        RecordResult(Assert::AssertEquals("移除_變為空", 0, manager.GetContainerCount()));
        RecordResult(Assert::AssertTrue("移除_確認為空", manager.IsEmpty()));

        delete manager;
    }

    // 測試查找容器
    void TestFindContainer()
    {
        Print("--- 測試查找容器 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("查找測試管理器");

        // 添加測試容器
        TradingPipelineContainer* container1 = new TradingPipelineContainer("查找測試1", "描述1", "Type1", TRADING_INIT);
        TradingPipelineContainer* container2 = new TradingPipelineContainer("查找測試2", "描述2", "Type2", TRADING_TICK);
        TradingPipelineContainer* container3 = new TradingPipelineContainer("查找測試3", "描述3", "Type3", TRADING_DEINIT);
        manager.AddContainer(container1);
        manager.AddContainer(container2);
        manager.AddContainer(container3);

        // 測試按名稱查找
        TradingPipelineContainer* found1 = manager.FindContainerByName("查找測試1");
        RecordResult(Assert::AssertNotNull("查找_按名稱存在1", found1));
        if(found1 != NULL)
        {
            RecordResult(Assert::AssertEquals("查找_名稱匹配1", "查找測試1", found1.GetName()));
        }

        TradingPipelineContainer* notFound = manager.FindContainerByName("不存在的容器");
        RecordResult(Assert::AssertNull("查找_按名稱不存在", notFound));

        // 測試按事件類型查找
        TradingPipelineContainer* foundInit = manager.FindContainerByEventType(TRADING_INIT);
        RecordResult(Assert::AssertNotNull("查找_按事件類型INIT", foundInit));
        if(foundInit != NULL)
        {
            RecordResult(Assert::AssertEquals("查找_事件類型匹配INIT", (int)TRADING_INIT, (int)foundInit.GetEventType()));
        }

        TradingPipelineContainer* foundTick = manager.FindContainerByEventType(TRADING_TICK);
        RecordResult(Assert::AssertNotNull("查找_按事件類型TICK", foundTick));
        if(foundTick != NULL)
        {
            RecordResult(Assert::AssertEquals("查找_事件類型匹配TICK", (int)TRADING_TICK, (int)foundTick.GetEventType()));
        }

        TradingPipelineContainer* foundDeinit = manager.FindContainerByEventType(TRADING_DEINIT);
        RecordResult(Assert::AssertNotNull("查找_按事件類型DEINIT", foundDeinit));
        if(foundDeinit != NULL)
        {
            RecordResult(Assert::AssertEquals("查找_事件類型匹配DEINIT", (int)TRADING_DEINIT, (int)foundDeinit.GetEventType()));
        }

        delete manager;
    }

    // 測試按事件類型執行
    void TestExecuteByEventType()
    {
        Print("--- 測試按事件類型執行 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("事件執行測試管理器");

        // 創建不同事件類型的容器
        TradingPipelineContainer* initContainer = new TradingPipelineContainer("初始化容器", "處理初始化", "InitContainer", TRADING_INIT);
        TradingPipelineContainer* tickContainer = new TradingPipelineContainer("Tick容器", "處理Tick", "TickContainer", TRADING_TICK);
        TradingPipelineContainer* deinitContainer = new TradingPipelineContainer("清理容器", "處理清理", "DeinitContainer", TRADING_DEINIT);

        // 向容器添加流水線
        TestManagerTradingPipeline* initPipeline = new TestManagerTradingPipeline("初始化流水線", "初始化處理");
        TestManagerTradingPipeline* tickPipeline = new TestManagerTradingPipeline("Tick流水線", "Tick處理");
        TestManagerTradingPipeline* deinitPipeline = new TestManagerTradingPipeline("清理流水線", "清理處理");

        initContainer.AddPipeline(initPipeline);
        tickContainer.AddPipeline(tickPipeline);
        deinitContainer.AddPipeline(deinitPipeline);

        manager.AddContainer(initContainer);
        manager.AddContainer(tickContainer);
        manager.AddContainer(deinitContainer);

        // 測試執行 TRADING_INIT 事件
        manager.Execute(TRADING_INIT);
        RecordResult(Assert::AssertTrue("事件執行_INIT容器已執行", initContainer.IsExecuted()));
        RecordResult(Assert::AssertFalse("事件執行_TICK容器未執行", tickContainer.IsExecuted()));
        RecordResult(Assert::AssertFalse("事件執行_DEINIT容器未執行", deinitContainer.IsExecuted()));

        // 測試執行 TRADING_TICK 事件
        manager.Execute(TRADING_TICK);
        RecordResult(Assert::AssertTrue("事件執行_TICK容器已執行", tickContainer.IsExecuted()));
        RecordResult(Assert::AssertFalse("事件執行_DEINIT容器仍未執行", deinitContainer.IsExecuted()));

        // 測試執行 TRADING_DEINIT 事件
        manager.Execute(TRADING_DEINIT);
        RecordResult(Assert::AssertTrue("事件執行_DEINIT容器已執行", deinitContainer.IsExecuted()));

        delete manager;
    }

    // 測試執行所有容器
    void TestExecuteAll()
    {
        Print("--- 測試執行所有容器 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("全部執行測試管理器");

        // 創建測試容器
        TradingPipelineContainer* container1 = new TradingPipelineContainer("全部執行容器1", "描述1", "Type1", TRADING_INIT);
        TradingPipelineContainer* container2 = new TradingPipelineContainer("全部執行容器2", "描述2", "Type2", TRADING_TICK);

        // 向容器添加流水線
        TestManagerTradingPipeline* pipeline1 = new TestManagerTradingPipeline("全部執行流水線1", "處理1");
        TestManagerTradingPipeline* pipeline2 = new TestManagerTradingPipeline("全部執行流水線2", "處理2");

        container1.AddPipeline(pipeline1);
        container2.AddPipeline(pipeline2);

        manager.AddContainer(container1);
        manager.AddContainer(container2);

        RecordResult(Assert::AssertFalse("全部執行_初始未執行", manager.IsExecuted()));

        // 執行所有容器
        manager.ExecuteAll();

        RecordResult(Assert::AssertTrue("全部執行_管理器已執行", manager.IsExecuted()));
        RecordResult(Assert::AssertTrue("全部執行_容器1已執行", container1.IsExecuted()));
        RecordResult(Assert::AssertTrue("全部執行_容器2已執行", container2.IsExecuted()));
        RecordResult(Assert::AssertTrue("全部執行_流水線1已執行", pipeline1.IsExecuted()));
        RecordResult(Assert::AssertTrue("全部執行_流水線2已執行", pipeline2.IsExecuted()));

        delete manager;
    }

    // 測試最大容器限制
    void TestMaxContainersLimit()
    {
        Print("--- 測試最大容器限制 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("限制測試管理器", "LimitTest", false, 2);

        RecordResult(Assert::AssertEquals("限制_最大容量", 2, manager.GetMaxContainers()));

        // 添加到最大容量
        TradingPipelineContainer* container1 = new TradingPipelineContainer("限制測試容器1");
        TradingPipelineContainer* container2 = new TradingPipelineContainer("限制測試容器2");
        TradingPipelineContainer* container3 = new TradingPipelineContainer("限制測試容器3");

        RecordResult(Assert::AssertTrue("限制_添加第1個", manager.AddContainer(container1)));
        RecordResult(Assert::AssertTrue("限制_添加第2個", manager.AddContainer(container2)));
        RecordResult(Assert::AssertTrue("限制_已滿", manager.IsFull()));

        // 嘗試添加超過限制
        RecordResult(Assert::AssertFalse("限制_超過限制", manager.AddContainer(container3)));
        RecordResult(Assert::AssertEquals("限制_數量不變", 2, manager.GetContainerCount()));

        delete container3;
        delete manager;
    }

    // 測試清理功能
    void TestClear()
    {
        Print("--- 測試清理功能 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("清理測試管理器");

        // 添加測試容器
        TradingPipelineContainer* container1 = new TradingPipelineContainer("清理測試容器1");
        TradingPipelineContainer* container2 = new TradingPipelineContainer("清理測試容器2");
        manager.AddContainer(container1);
        manager.AddContainer(container2);

        RecordResult(Assert::AssertEquals("清理_添加後數量", 2, manager.GetContainerCount()));
        RecordResult(Assert::AssertFalse("清理_添加後非空", manager.IsEmpty()));

        // 執行清理
        manager.Clear();

        RecordResult(Assert::AssertEquals("清理_清理後數量", 0, manager.GetContainerCount()));
        RecordResult(Assert::AssertTrue("清理_清理後為空", manager.IsEmpty()));
        RecordResult(Assert::AssertTrue("清理_清理後有空位", manager.HasEmptySlot()));

        delete manager;
    }

    // 測試邊界情況
    void TestEdgeCases()
    {
        Print("--- 測試邊界情況 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("邊界測試管理器");

        // 測試空管理器執行
        manager.ExecuteAll();
        // 根據實現邏輯，空管理器執行後仍然會設置執行狀態為 true
        RecordResult(Assert::AssertTrue("邊界_空管理器執行", manager.IsExecuted()));

        // 測試禁用管理器 - 創建新的管理器來測試禁用狀態
        TradingPipelineContainerManager* disabledManager = new TradingPipelineContainerManager("禁用測試管理器");
        disabledManager.SetEnabled(false);
        TradingPipelineContainer* container = new TradingPipelineContainer("邊界測試容器");
        disabledManager.AddContainer(container);
        disabledManager.ExecuteAll();
        RecordResult(Assert::AssertFalse("邊界_禁用管理器不執行", disabledManager.IsExecuted()));
        delete disabledManager;

        // 重新啟用
        manager.SetEnabled(true);
        manager.ExecuteAll();
        RecordResult(Assert::AssertTrue("邊界_重新啟用執行", manager.IsExecuted()));

        delete manager;
    }

    // 測試複雜場景
    void TestComplexScenario()
    {
        Print("--- 測試複雜場景 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("複雜場景管理器");

        // 創建完整的 EA 生命週期容器
        TradingPipelineContainer* initContainer = new TradingPipelineContainer("EA初始化", "EA啟動處理", "EAInit", TRADING_INIT);
        TradingPipelineContainer* tickContainer = new TradingPipelineContainer("EA主處理", "每個Tick處理", "EATick", TRADING_TICK);
        TradingPipelineContainer* deinitContainer = new TradingPipelineContainer("EA清理", "EA關閉處理", "EADeinit", TRADING_DEINIT);

        // 向每個容器添加多個流水線
        TestManagerTradingPipeline* initPipeline1 = new TestManagerTradingPipeline("參數初始化", "初始化參數");
        TestManagerTradingPipeline* initPipeline2 = new TestManagerTradingPipeline("連接初始化", "初始化連接");

        TestManagerTradingPipeline* tickPipeline1 = new TestManagerTradingPipeline("數據獲取", "獲取市場數據");
        TestManagerTradingPipeline* tickPipeline2 = new TestManagerTradingPipeline("信號分析", "分析交易信號");
        TestManagerTradingPipeline* tickPipeline3 = new TestManagerTradingPipeline("訂單處理", "處理交易訂單");

        TestManagerTradingPipeline* deinitPipeline1 = new TestManagerTradingPipeline("保存狀態", "保存EA狀態");
        TestManagerTradingPipeline* deinitPipeline2 = new TestManagerTradingPipeline("關閉連接", "關閉所有連接");

        initContainer.AddPipeline(initPipeline1);
        initContainer.AddPipeline(initPipeline2);

        tickContainer.AddPipeline(tickPipeline1);
        tickContainer.AddPipeline(tickPipeline2);
        tickContainer.AddPipeline(tickPipeline3);

        deinitContainer.AddPipeline(deinitPipeline1);
        deinitContainer.AddPipeline(deinitPipeline2);

        manager.AddContainer(initContainer);
        manager.AddContainer(tickContainer);
        manager.AddContainer(deinitContainer);

        // 模擬 EA 生命週期
        // 1. 初始化階段
        manager.Execute(TRADING_INIT);
        RecordResult(Assert::AssertTrue("複雜_初始化容器執行", initContainer.IsExecuted()));
        RecordResult(Assert::AssertTrue("複雜_初始化流水線1執行", initPipeline1.IsExecuted()));
        RecordResult(Assert::AssertTrue("複雜_初始化流水線2執行", initPipeline2.IsExecuted()));

        // 2. Tick 處理階段
        manager.Execute(TRADING_TICK);
        RecordResult(Assert::AssertTrue("複雜_Tick容器執行", tickContainer.IsExecuted()));
        RecordResult(Assert::AssertTrue("複雜_Tick流水線1執行", tickPipeline1.IsExecuted()));
        RecordResult(Assert::AssertTrue("複雜_Tick流水線2執行", tickPipeline2.IsExecuted()));
        RecordResult(Assert::AssertTrue("複雜_Tick流水線3執行", tickPipeline3.IsExecuted()));

        // 3. 清理階段
        manager.Execute(TRADING_DEINIT);
        RecordResult(Assert::AssertTrue("複雜_清理容器執行", deinitContainer.IsExecuted()));
        RecordResult(Assert::AssertTrue("複雜_清理流水線1執行", deinitPipeline1.IsExecuted()));
        RecordResult(Assert::AssertTrue("複雜_清理流水線2執行", deinitPipeline2.IsExecuted()));

        // 驗證管理器狀態
        RecordResult(Assert::AssertEquals("複雜_容器總數", 3, manager.GetContainerCount()));
        RecordResult(Assert::AssertTrue("複雜_管理器已執行", manager.IsExecuted()));

        delete manager;
    }
};

//+------------------------------------------------------------------+
//| 運行 TradingPipelineContainerManager 測試                        |
//+------------------------------------------------------------------+
void RunTestTradingPipelineContainerManager()
{
    Print("\n" + StringRepeat("=", 70));
    Print("  TradingPipelineContainerManager 單元測試");
    Print(StringRepeat("=", 70));

    TestRunner* runner = new TestRunner();
    TestTradingPipelineContainerManagerCase* testCase = new TestTradingPipelineContainerManagerCase();

    runner.RunTestCase(testCase);
    runner.ShowSummary();

    delete testCase;
    delete runner;

    Print(StringRepeat("=", 70));
    Print("  TradingPipelineContainerManager 單元測試完成");
    Print(StringRepeat("=", 70));
}
